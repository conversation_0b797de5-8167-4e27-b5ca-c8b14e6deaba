# CircularProgress 圆形进度条组件

基于 ECharts 实现的圆形进度条组件，采用双 pie 图层设计，实现底部断开的空心效果。

## 特性

- ✅ 底部断开的空心设计（借鉴 option.js 的双 pie 图层思想）
- ✅ 支持渐变色和预设颜色方案
- ✅ 响应式设计，支持动态数据更新
- ✅ 可自定义尺寸、颜色、标签等
- ✅ 流畅的动画效果
- ✅ TypeScript 支持

## 基本用法

```vue
<template>
  <CircularProgress :value="75" />
</template>

<script setup>
import CircularProgress from '@/components/CircularProgress/CircularProgress.vue';
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 进度值 (0-100) | `number` | `0` |
| size | 组件大小 (px) | `number` | `64` |
| color | 进度条颜色 | `string \| string[]` | `'#3B82F6'` |
| label | 显示标签 | `string` | `''` |
| showValue | 是否显示数值 | `boolean` | `true` |
| thickness | 进度条粗细 | `number` | `8` |
| backgroundColor | 背景色 | `string` | `'#E5E7EB'` |
| fontSize | 字体大小 | `number` | `12` |
| animationDuration | 动画持续时间 (ms) | `number` | `1000` |

### 预设颜色方案

| 颜色名称 | 渐变色 | 用途 |
|----------|--------|------|
| `cpu` | `['#00a2ff', '#70ffac']` | CPU 使用率 |
| `memory` | `['#10B981', '#34D399']` | 内存使用率 |
| `gpu` | `['#F59E0B', '#FBBF24']` | GPU 使用率 |
| `primary` | `['#3B82F6', '#60A5FA']` | 主色调 |
| `success` | `['#10B981', '#34D399']` | 成功状态 |
| `warning` | `['#F59E0B', '#FBBF24']` | 警告状态 |
| `danger` | `['#EF4444', '#F87171']` | 危险状态 |

## 使用示例

### 基础用法
```vue
<CircularProgress :value="75" />
```

### 预设颜色
```vue
<CircularProgress :value="85" color="cpu" label="CPU" />
<CircularProgress :value="67" color="memory" label="内存" />
<CircularProgress :value="42" color="gpu" label="GPU" />
```

### 自定义渐变色
```vue
<CircularProgress 
  :value="80" 
  :color="['#FF6B6B', '#FF8E8E']" 
  label="自定义" 
/>
```

### 不同尺寸
```vue
<CircularProgress :value="75" :size="48" :fontSize="10" />
<CircularProgress :value="75" :size="96" :fontSize="16" />
<CircularProgress :value="75" :size="128" :fontSize="20" />
```

### 动态更新
```vue
<template>
  <CircularProgress :value="dynamicValue" color="primary" />
</template>

<script setup>
import { ref } from 'vue';

const dynamicValue = ref(50);

// 动态更新值
setInterval(() => {
  dynamicValue.value = Math.floor(Math.random() * 100);
}, 1000);
</script>
```

## 设计原理

组件采用双 pie 图层设计：

1. **背景层 (z-index: 9)**：显示 75% 的圆弧背景，底部断开 25%
2. **前景层 (z-index: 10)**：显示实际进度，支持渐变色

### 算法说明

- 总圆弧范围：75%（底部断开 25%）
- 进度映射：将 0-100% 的进度值映射到 0-75 的圆弧范围
- 计算公式：`progressValue = (75 * value) / 100`

## 在 ModelServerStatus.vue 中的应用

```vue
<CircularProgress
  :value="parseFloat(server.cpuPercentage || '0')"
  :size="64"
  color="cpu"
  label="CPU"
  :fontSize="10"
/>
```

## 注意事项

1. 组件基于 ECharts，确保项目中已正确配置 ECharts
2. 进度值范围为 0-100，超出范围会被自动限制
3. 渐变色数组需要包含至少两个颜色值
4. 组件会自动处理响应式更新和动画效果
