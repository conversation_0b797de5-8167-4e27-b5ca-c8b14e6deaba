<template>
  <div class="p-8 bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">圆形进度条组件示例</h1>
      
      <!-- 基础用法 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">基础用法 - 底部断开空心效果</h2>
        <div class="flex items-center space-x-8">
          <div class="text-center">
            <CircularProgress :value="75" />
            <p class="mt-2 text-sm text-gray-600">默认样式 75%</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="60" color="success" />
            <p class="mt-2 text-sm text-gray-600">成功色 60%</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="30" color="warning" />
            <p class="mt-2 text-sm text-gray-600">警告色 30%</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="90" color="danger" />
            <p class="mt-2 text-sm text-gray-600">危险色 90%</p>
          </div>
        </div>
      </div>

      <!-- 预设颜色方案 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">预设颜色方案</h2>
        <div class="flex items-center space-x-8">
          <div class="text-center">
            <CircularProgress :value="85" color="cpu" label="CPU" />
            <p class="mt-2 text-sm text-gray-600">CPU 使用率</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="67" color="memory" label="内存" />
            <p class="mt-2 text-sm text-gray-600">内存使用率</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="42" color="gpu" label="GPU" />
            <p class="mt-2 text-sm text-gray-600">GPU 使用率</p>
          </div>
        </div>
      </div>

      <!-- 不同尺寸 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">不同尺寸</h2>
        <div class="flex items-center space-x-8">
          <div class="text-center">
            <CircularProgress :value="75" :size="48" :fontSize="10" />
            <p class="mt-2 text-sm text-gray-600">小尺寸 (48px)</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="75" :size="64" :fontSize="12" />
            <p class="mt-2 text-sm text-gray-600">中等尺寸 (64px)</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="75" :size="96" :fontSize="16" />
            <p class="mt-2 text-sm text-gray-600">大尺寸 (96px)</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="75" :size="128" :fontSize="20" />
            <p class="mt-2 text-sm text-gray-600">超大尺寸 (128px)</p>
          </div>
        </div>
      </div>

      <!-- 自定义渐变色 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">自定义渐变色</h2>
        <div class="flex items-center space-x-8">
          <div class="text-center">
            <CircularProgress 
              :value="80" 
              :color="['#FF6B6B', '#FF8E8E']" 
              label="自定义1" 
            />
            <p class="mt-2 text-sm text-gray-600">红色渐变</p>
          </div>
          <div class="text-center">
            <CircularProgress 
              :value="65" 
              :color="['#4ECDC4', '#44A08D']" 
              label="自定义2" 
            />
            <p class="mt-2 text-sm text-gray-600">青色渐变</p>
          </div>
          <div class="text-center">
            <CircularProgress 
              :value="90" 
              :color="['#A8E6CF', '#7FCDCD']" 
              label="自定义3" 
            />
            <p class="mt-2 text-sm text-gray-600">绿色渐变</p>
          </div>
        </div>
      </div>

      <!-- 不同进度值展示 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">不同进度值展示</h2>
        <div class="grid grid-cols-5 gap-6">
          <div class="text-center">
            <CircularProgress :value="0" color="cpu" label="0%" />
            <p class="mt-2 text-sm text-gray-600">空状态</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="25" color="memory" label="25%" />
            <p class="mt-2 text-sm text-gray-600">低使用率</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="50" color="gpu" label="50%" />
            <p class="mt-2 text-sm text-gray-600">中等使用率</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="75" color="warning" label="75%" />
            <p class="mt-2 text-sm text-gray-600">高使用率</p>
          </div>
          <div class="text-center">
            <CircularProgress :value="100" color="danger" label="100%" />
            <p class="mt-2 text-sm text-gray-600">满载状态</p>
          </div>
        </div>
      </div>

      <!-- 动态更新 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">动态更新</h2>
        <div class="flex items-center space-x-8 mb-4">
          <div class="text-center">
            <CircularProgress
              :value="dynamicValue"
              color="primary"
              label="动态值"
            />
            <p class="mt-2 text-sm text-gray-600">当前值: {{ dynamicValue }}%</p>
          </div>
        </div>
        <div class="flex space-x-4">
          <button
            @click="updateValue"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            随机更新
          </button>
          <button
            @click="startAnimation"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            开始动画
          </button>
          <button
            @click="stopAnimation"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            停止动画
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onUnmounted } from 'vue';
  import CircularProgress from './CircularProgress.vue';

  defineOptions({ name: 'CircularProgressDemo' });

  const dynamicValue = ref(50);
  let animationTimer: NodeJS.Timeout | null = null;

  // 随机更新值
  const updateValue = () => {
    dynamicValue.value = Math.floor(Math.random() * 100);
  };

  // 开始动画
  const startAnimation = () => {
    if (animationTimer) return;
    
    animationTimer = setInterval(() => {
      dynamicValue.value = Math.floor(Math.random() * 100);
    }, 1000);
  };

  // 停止动画
  const stopAnimation = () => {
    if (animationTimer) {
      clearInterval(animationTimer);
      animationTimer = null;
    }
  };

  onUnmounted(() => {
    stopAnimation();
  });
</script>

<style scoped>
  /* 组件样式已在 CircularProgress.vue 中定义 */
</style>
