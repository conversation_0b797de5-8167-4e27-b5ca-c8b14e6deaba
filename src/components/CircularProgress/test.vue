<template>
  <div class="p-8 bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">圆形进度条测试</h1>
      
      <!-- 测试不同进度值 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">进度值测试 (0% - 100%)</h2>
        <div class="grid grid-cols-6 gap-4">
          <div v-for="value in testValues" :key="value" class="text-center">
            <CircularProgress 
              :value="value" 
              :color="getColorByValue(value)"
              :label="`${value}%`"
              :size="80"
              :fontSize="10"
            />
            <p class="mt-2 text-xs text-gray-600">{{ value }}%</p>
          </div>
        </div>
      </div>

      <!-- 动态测试 -->
      <div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">动态测试</h2>
        <div class="flex items-center space-x-8 mb-6">
          <div class="text-center">
            <CircularProgress 
              :value="currentValue" 
              color="primary"
              label="测试"
              :size="120"
              :fontSize="14"
            />
            <p class="mt-2 text-sm text-gray-600">当前值: {{ currentValue }}%</p>
          </div>
        </div>
        
        <!-- 滑块控制 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            调整进度值: {{ currentValue }}%
          </label>
          <input 
            v-model="currentValue" 
            type="range" 
            min="0" 
            max="100" 
            step="1"
            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>

        <!-- 预设值按钮 -->
        <div class="flex flex-wrap gap-2">
          <button 
            v-for="preset in presetValues" 
            :key="preset"
            @click="currentValue = preset"
            class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
          >
            {{ preset }}%
          </button>
        </div>
      </div>

      <!-- 算法验证 -->
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">算法验证</h2>
        <div class="text-sm text-gray-600 space-y-2">
          <p><strong>当前进度:</strong> {{ currentValue }}%</p>
          <p><strong>映射到圆弧值:</strong> {{ (75 * currentValue / 100).toFixed(2) }}</p>
          <p><strong>剩余圆弧值:</strong> {{ (75 - (75 * currentValue / 100)).toFixed(2) }}</p>
          <p><strong>总圆弧范围:</strong> 75 (底部断开25)</p>
          <p class="text-green-600">
            <strong>验证:</strong> 
            {{ (75 * currentValue / 100).toFixed(2) }} + {{ (75 - (75 * currentValue / 100)).toFixed(2) }} = 75 ✓
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import CircularProgress from './CircularProgress.vue';

  defineOptions({ name: 'CircularProgressTest' });

  const currentValue = ref(50);
  
  // 测试用的进度值
  const testValues = [0, 10, 25, 40, 50, 60, 75, 85, 95, 100];
  
  // 预设值
  const presetValues = [0, 25, 50, 75, 100];

  // 根据进度值获取颜色
  const getColorByValue = (value: number) => {
    if (value === 0) return '#9CA3AF'; // 灰色
    if (value <= 30) return 'success'; // 绿色
    if (value <= 60) return 'primary'; // 蓝色
    if (value <= 80) return 'warning'; // 橙色
    return 'danger'; // 红色
  };
</script>

<style scoped>
  /* 自定义滑块样式 */
  input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3B82F6;
    cursor: pointer;
  }

  input[type="range"]::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3B82F6;
    cursor: pointer;
    border: none;
  }
</style>
