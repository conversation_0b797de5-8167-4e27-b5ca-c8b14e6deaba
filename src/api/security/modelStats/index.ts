import { securityHttp } from '@/utils/http/axios';
import { 
  BiCountModelVo, 
  BiModelServerHealthVo, 
  BiModelServerApiCallVo,
  ModelStatsQuery 
} from './model';

enum Api {
  selectModelStats = '/bi/model/selectModelStats',
  selectModelServerStats = '/bi/model/selectModelServerStats',
  selectModelServerTrend = '/bi/model/selectModelServerTrend',
}

/**
 * 获取服务健康监控统计
 */
export function selectModelStats() {
  return securityHttp.get<BiCountModelVo>({ url: Api.selectModelStats });
}

/**
 * 获取模型服务监控
 * @param params 查询参数
 */
export function selectModelServerStats(params?: ModelStatsQuery) {
  return securityHttp.get<BiModelServerHealthVo[]>({ 
    url: Api.selectModelServerStats, 
    params 
  });
}

/**
 * 获取模型调用趋势
 * @param params 查询参数
 */
export function selectModelServerTrend(params?: ModelStatsQuery) {
  return securityHttp.get<BiModelServerApiCallVo[]>({ 
    url: Api.selectModelServerTrend, 
    params 
  });
}
