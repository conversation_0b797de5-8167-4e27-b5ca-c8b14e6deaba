import { BaseEntity } from '@/api/base';

/**
 * 模型统计数据
 */
export interface BiCountModelVo {
  /** 接口调用成功率 */
  apiCallSuccessRate: string;
  /** 平均响应时间 */
  averageResponseTime: string;
  /** 今日调用次数 */
  todayCallCount: string;
  /** 总调用次数 */
  totalCallCount: string;
}

/**
 * 模型服务健康状态
 */
export interface BiModelServerHealthVo extends BaseEntity {
  /** 模型名称 */
  modelName: string;
  /** 模型英文名称 */
  modelNameEn: string;
  /** 模型版本 */
  modelVersion: string;
  /** 模型运行IP */
  modelRunIp: string;
  /** 采集时间：yyyy-MM-dd HH:mm:ss */
  collectTime: string;
  /** CPU百分比 */
  cpuPercentage: string;
  /** 内存百分比 */
  memoryPercentage: string;
  /** GPU百分比 */
  gpuPercentage: string;
  /** 备注 */
  remark: string;
  /** 版本号 */
  version: number;
}

/**
 * 模型服务API调用统计
 */
export interface BiModelServerApiCallVo extends BaseEntity {
  /** 模型名称 */
  modelName: string;
  /** 模型英文名称 */
  modelNameEn: string;
  /** 模型版本 */
  modelVersion: string;
  /** 业务类型代码 ProtocolUtils.EventType */
  eventTypeCode: string;
  /** 业务类型描述 */
  eventTypeDesc: string;
  /** 采集时间：yyyy-MM-dd HH */
  collectTime: string;
  /** 调用成功 */
  callSuccess: number;
  /** 调用失败 */
  callFailure: number;
  /** 备注 */
  remark: string;
  /** 版本号 */
  version: number;
}

/**
 * 模型统计查询参数
 */
export interface ModelStatsQuery {
  /** 模型名称 */
  modelName?: string;
  /** 趋势维度：1按小时 2按天 3按周 */
  trentDimension?: string;
}
