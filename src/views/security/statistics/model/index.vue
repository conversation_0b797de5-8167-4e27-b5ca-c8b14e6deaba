<template>
  <div class="model-statistics-page min-h-screen bg-gray-50">
    <!-- 顶部统计信息栏 -->
    <ModelStatsOverview ref="statsOverviewRef" :search-params="searchParams" />

    <!-- 中间模型服务状态区 -->
    <ModelServerStatus ref="serverStatusRef" :search-params="searchParams" />

    <!-- 底部模型调用趋势图表区 -->
    <ModelCallTrend ref="callTrendRef" :search-params="searchParams" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import ModelStatsOverview from './components/ModelStatsOverview.vue';
  import ModelServerStatus from './components/ModelServerStatus.vue';
  import ModelCallTrend from './components/ModelCallTrend.vue';

  defineOptions({ name: 'ModelStatistics' });

  // 组件引用
  const statsOverviewRef = ref();
  const serverStatusRef = ref();
  const callTrendRef = ref();

  // 搜索参数
  const searchParams = reactive({
    // 这里可以添加全局搜索参数
    // 比如时间范围、站点等
  });

  // 刷新所有组件数据
  function refreshAllData() {
    statsOverviewRef.value?.refresh(searchParams);
    serverStatusRef.value?.refresh();
    callTrendRef.value?.refresh();
  }

  // 页面初始化
  onMounted(() => {
    // 可以在这里进行一些初始化操作
    console.log('模型统计页面已加载');
  });

  // 暴露刷新方法供外部调用
  defineExpose({
    refresh: refreshAllData,
  });
</script>

<style scoped>
  .model-statistics-page {
    /* 页面整体样式 */
  }
</style>
