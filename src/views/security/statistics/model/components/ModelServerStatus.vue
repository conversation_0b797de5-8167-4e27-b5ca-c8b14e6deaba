<template>
  <div class="model-server-status bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 标题和筛选控件 -->
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold text-gray-800">模型服务状态</h3>
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">IP:</span>
          <a-select
            v-model:value="selectedIp"
            placeholder="选择IP"
            style="width: 150px"
            @change="handleIpChange"
          >
            <a-select-option value="">所有IP</a-select-option>
            <a-select-option value="*************">*************</a-select-option>
            <a-select-option value="*************">*************</a-select-option>
          </a-select>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">模型:</span>
          <a-select
            v-model:value="selectedModel"
            placeholder="选择模型"
            style="width: 150px"
            @change="handleModelChange"
          >
            <a-select-option value="">所有模型</a-select-option>
            <a-select-option value="穿戴检测模型">穿戴检测模型</a-select-option>
            <a-select-option value="安全帽检测模型">安全帽检测模型</a-select-option>
          </a-select>
        </div>
      </div>
    </div>

    <!-- 模型服务卡片列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div
        v-for="(server, index) in serverList"
        :key="index"
        class="server-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <!-- 卡片头部 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-2">
            <!-- 状态指示器 -->
            <div :class="['w-3 h-3 rounded-full', getStatusColor(server.status)]"></div>
            <div>
              <h4 class="font-medium text-gray-900 text-sm">{{ server.modelName }}</h4>
              <p class="text-xs text-gray-500">{{ server.serverName || '服务器A' }}</p>
            </div>
          </div>
          <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
            {{ server.modelVersion }}
          </span>
        </div>

        <!-- 资源使用率圆形进度条 -->
        <div class="flex items-center justify-center space-x-4 mb-4">
          <div class="text-center">
            <div :ref="(el) => setChartRef(el, index, 'cpu')" class="w-16 h-16 mx-auto"></div>
            <div class="mt-1 text-xs text-gray-600">CPU</div>
          </div>
          <div class="text-center">
            <div :ref="(el) => setChartRef(el, index, 'memory')" class="w-16 h-16 mx-auto"></div>
            <div class="mt-1 text-xs text-gray-600">内存</div>
          </div>
          <div class="text-center">
            <div :ref="(el) => setChartRef(el, index, 'gpu')" class="w-16 h-16 mx-auto"></div>
            <div class="mt-1 text-xs text-gray-600">GPU</div>
          </div>
        </div>

        <!-- 底部信息和操作 -->
        <div class="flex items-center justify-between">
          <div class="text-xs text-gray-500"> 最后更新: {{ formatTime(server.collectTime) }} </div>
          <div class="flex items-center space-x-1">
            <a-button size="small" type="text" @click="handleView(server)">
              <template #icon>
                <Icon icon="ant-design:eye-outlined" class="text-xs" />
              </template>
            </a-button>
            <a-button size="small" type="text" @click="handleRefresh(server)">
              <template #icon>
                <Icon icon="ant-design:reload-outlined" class="text-xs" />
              </template>
            </a-button>
            <a-button size="small" type="text" danger @click="handleStop(server)">
              <template #icon>
                <Icon icon="ant-design:stop-outlined" class="text-xs" />
              </template>
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="serverList.length === 0" class="text-center py-12">
      <Icon icon="ant-design:inbox-outlined" class="text-4xl text-gray-400 mb-2" />
      <p class="text-gray-500">暂无模型服务数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { Select as ASelect, Button as AButton, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { selectModelServerStats } from '@/api/security/modelStats';
  import type { BiModelServerHealthVo } from '@/api/security/modelStats/model';

  defineOptions({ name: 'ModelServerStatus' });

  // 扩展服务器数据接口
  interface ExtendedServerData extends BiModelServerHealthVo {
    serverName?: string;
    status?: 'normal' | 'error' | 'warning';
  }

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const selectedIp = ref('');
  const selectedModel = ref('');
  const serverList = ref<ExtendedServerData[]>([]);
  const chartRefs = ref<Map<string, HTMLElement>>(new Map());

  // 设置图表引用
  function setChartRef(el: any, index: number, type: 'cpu' | 'memory' | 'gpu') {
    if (el && el instanceof HTMLElement) {
      const key = `${index}-${type}`;
      chartRefs.value.set(key, el);
    }
  }

  // 获取状态颜色
  function getStatusColor(status?: string) {
    switch (status) {
      case 'normal':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  }

  // 格式化时间
  function formatTime(time?: string) {
    if (!time) return '--';
    try {
      const date = new Date(time);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
    } catch {
      return time;
    }
  }

  // 创建圆形进度条
  function createGaugeChart(element: HTMLElement, value: number, color: string, label: string) {
    const { setOptions } = useECharts(ref(element as HTMLDivElement));

    const option: any = {
      series: [
        {
          type: 'gauge',
          radius: '90%',
          startAngle: 90,
          endAngle: -270,
          pointer: { show: false },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 0,
              color: color,
            },
          },
          axisLine: {
            lineStyle: {
              width: 8,
              color: [[1, '#E5E7EB']],
            },
          },
          splitLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
          detail: {
            valueAnimation: true,
            formatter: '{value}%',
            color: '#374151',
            fontSize: 18,
            fontWeight: 'bold',
            offsetCenter: [0, 0],
          },
          data: [{ value: value, name: label }],
        },
      ],
    };

    setOptions(option);
  }

  // 获取服务器列表数据
  async function fetchServerList() {
    try {
      const params = {
        modelName: selectedModel.value || undefined,
        // 这里可以根据需要添加IP筛选参数
      };

      const response = await selectModelServerStats(params);
      const rawServerList = response || [];

      // 扩展服务器数据
      serverList.value = rawServerList.map((server, index) => ({
        ...server,
        serverName: `服务器${String.fromCharCode(65 + index)}`, // A, B, C, D...
        status: Math.random() > 0.8 ? 'error' : ('normal' as const),
      }));

      await updateCharts();
    } catch (error) {
      console.error('获取模型服务状态失败:', error);
      serverList.value = [];
    }
  }

  // 更新所有图表
  async function updateCharts() {
    await nextTick();

    serverList.value.forEach((server, index) => {
      // CPU 图表
      const cpuElement = chartRefs.value.get(`${index}-cpu`);
      if (cpuElement) {
        createGaugeChart(cpuElement, parseFloat(server.cpuPercentage || '0'), '#3B82F6', 'CPU');
      }

      // 内存图表
      const memoryElement = chartRefs.value.get(`${index}-memory`);
      if (memoryElement) {
        createGaugeChart(
          memoryElement,
          parseFloat(server.memoryPercentage || '0'),
          '#10B981',
          '内存',
        );
      }

      // GPU 图表
      const gpuElement = chartRefs.value.get(`${index}-gpu`);
      if (gpuElement) {
        createGaugeChart(gpuElement, parseFloat(server.gpuPercentage || '0'), '#F59E0B', 'GPU');
      }
    });
  }

  // IP变化处理
  function handleIpChange() {
    fetchServerList();
  }

  // 模型变化处理
  function handleModelChange() {
    fetchServerList();
  }

  // 查看操作
  function handleView(server: ExtendedServerData) {
    message.info(`查看服务器: ${server.modelName} - ${server.serverName}`);
  }

  // 刷新操作
  function handleRefresh(server: ExtendedServerData) {
    message.info(`刷新服务器: ${server.modelName} - ${server.serverName}`);
    fetchServerList();
  }

  // 停止操作
  function handleStop(server: ExtendedServerData) {
    message.warning(`停止服务器: ${server.modelName} - ${server.serverName}`);
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    () => {
      fetchServerList();
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchServerList();
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => fetchServerList(),
  });
</script>

<style scoped>
  .server-card {
    transition: all 0.3s ease;
  }

  .server-card:hover {
    transform: translateY(-1px);
  }
</style>
