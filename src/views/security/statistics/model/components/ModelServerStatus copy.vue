<template>
  <div class="model-server-status bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 模型服务卡片网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div
        v-for="(server, index) in serverList"
        :key="index"
        class="server-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <!-- 卡片头部 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-2">
            <!-- 状态指示器 -->
            <div :class="['w-3 h-3 rounded-full', getStatusColor(server.status)]"></div>
            <div>
              <h4 class="font-medium text-gray-900 text-sm">{{ server.modelName }}</h4>
              <p class="text-xs text-gray-500">{{ server.serverName || '服务器A' }}</p>
            </div>
          </div>
          <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
            {{ server.modelVersion }}
          </span>
        </div>

        <!-- 资源使用率圆形进度条 -->
        <div class="flex items-center justify-center space-x-4 mb-4">
          <div class="text-center">
            <div :ref="(el) => setChartRef(el, index, 'cpu')" class="w-16 h-16 mx-auto"></div>
            <div class="mt-1 text-xs text-gray-600">CPU</div>
          </div>
          <div class="text-center">
            <div :ref="(el) => setChartRef(el, index, 'memory')" class="w-16 h-16 mx-auto"></div>
            <div class="mt-1 text-xs text-gray-600">内存</div>
          </div>
          <div class="text-center">
            <div :ref="(el) => setChartRef(el, index, 'gpu')" class="w-16 h-16 mx-auto"></div>
            <div class="mt-1 text-xs text-gray-600">GPU</div>
          </div>
        </div>

        <!-- 底部信息和操作 -->
        <div class="flex items-center justify-between">
          <div class="text-xs text-gray-500"> 最后更新: {{ formatTime(server.collectTime) }} </div>
          <div class="flex items-center space-x-1">
            <a-button size="small" type="text" @click="handleView(server)">
              <template #icon>
                <Icon icon="ant-design:eye-outlined" class="text-xs" />
              </template>
            </a-button>
            <a-button size="small" type="text" @click="handleRefresh(server)">
              <template #icon>
                <Icon icon="ant-design:reload-outlined" class="text-xs" />
              </template>
            </a-button>
            <a-button size="small" type="text" danger @click="handleStop(server)">
              <template #icon>
                <Icon icon="ant-design:stop-outlined" class="text-xs" />
              </template>
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-12">
      <Icon icon="ant-design:loading-outlined" class="text-2xl text-gray-400 mb-2 animate-spin" />
      <p class="text-gray-500">加载中...</p>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && serverList.length === 0" class="text-center py-12">
      <Icon icon="ant-design:inbox-outlined" class="text-4xl text-gray-400 mb-2" />
      <p class="text-gray-500">暂无模型服务数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { Button as AButton, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { selectModelServerStats } from '@/api/security/modelStats';
  import type { BiModelServerHealthVo } from '@/api/security/modelStats/model';

  defineOptions({ name: 'ModelServerStatus' });

  // 扩展服务器数据接口
  interface ExtendedServerData extends BiModelServerHealthVo {
    serverName?: string;
    status?: 'normal' | 'error' | 'warning';
  }

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const loading = ref(false);
  const serverList = ref<ExtendedServerData[]>([]);
  const chartRefs = ref<Map<string, HTMLElement>>(new Map());

  // 设置图表引用
  function setChartRef(el: HTMLElement | null, index: number, type: 'cpu' | 'memory' | 'gpu') {
    if (el) {
      const key = `${index}-${type}`;
      chartRefs.value.set(key, el);
    }
  }

  // 获取状态颜色
  function getStatusColor(status?: string) {
    switch (status) {
      case 'normal':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  }

  // 格式化时间
  function formatTime(time?: string) {
    if (!time) return '--';
    try {
      const date = new Date(time);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
    } catch {
      return time;
    }
  }

  // 创建圆形进度条
  function createGaugeChart(element: HTMLElement, value: number, color: string, label: string) {
    const { setOptions } = useECharts(ref(element as HTMLDivElement));

    const option: any = {
      series: [
        {
          type: 'gauge',
          radius: '90%',
          startAngle: 90,
          endAngle: -270,
          pointer: { show: false },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 0,
              color: color,
            },
          },
          axisLine: {
            lineStyle: {
              width: 8,
              color: [[1, '#E5E7EB']],
            },
          },
          splitLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
          detail: {
            valueAnimation: true,
            formatter: '{value}%',
            color: '#374151',
            fontSize: 18,
            fontWeight: 'bold',
            offsetCenter: [0, 0],
          },
          data: [{ value: value, name: label }],
        },
      ],
    };

    setOptions(option);
  }

  // 获取服务器数据
  async function fetchServerData() {
    try {
      loading.value = true;
      const params = {
        modelName: undefined,
      };

      const response = await selectModelServerStats(params);
      const rawServerList = response || [];

      // 如果没有数据，创建一些模拟数据用于演示
      if (rawServerList.length === 0) {
        serverList.value = createMockData();
      } else {
        // 扩展服务器数据
        serverList.value = rawServerList.map((server, index) => ({
          ...server,
          serverName: `服务器${String.fromCharCode(65 + index)}`, // A, B, C, D...
          status: Math.random() > 0.8 ? 'error' : ('normal' as const),
        }));
      }

      await updateCharts();
    } catch (error) {
      console.error('获取模型服务状态失败:', error);
      // 出错时显示模拟数据
      serverList.value = createMockData();
      await updateCharts();
    } finally {
      loading.value = false;
    }
  }

  // 创建模拟数据
  function createMockData(): ExtendedServerData[] {
    const modelNames = [
      '穿戴检测模型',
      '行为分析模型',
      '工具识别模型',
      '车辆检测模型',
      '目标检测模型',
    ];
    const versions = ['v2.3.0', 'v1.8.2', 'v3.1.0', 'v2.0.1', 'v1.5.3'];

    return Array.from({ length: 5 }, (_, index) => ({
      modelName: modelNames[index],
      modelNameEn: `model-${index + 1}`,
      modelVersion: versions[index],
      modelRunIp: `192.168.1.${100 + index}`,
      collectTime: new Date(Date.now() - Math.random() * 3600000).toISOString(),
      cpuPercentage: String(Math.floor(Math.random() * 100)),
      memoryPercentage: String(Math.floor(Math.random() * 100)),
      gpuPercentage: String(Math.floor(Math.random() * 100)),
      remark: '',
      version: 1,
      serverName: `服务器${String.fromCharCode(65 + index)}`,
      status: Math.random() > 0.7 ? 'error' : ('normal' as const),
    }));
  }

  // 更新所有图表
  async function updateCharts() {
    await nextTick();

    serverList.value.forEach((server, index) => {
      // CPU 图表
      const cpuElement = chartRefs.value.get(`${index}-cpu`);
      if (cpuElement) {
        createGaugeChart(cpuElement, parseFloat(server.cpuPercentage || '0'), '#3B82F6', 'CPU');
      }

      // 内存图表
      const memoryElement = chartRefs.value.get(`${index}-memory`);
      if (memoryElement) {
        createGaugeChart(
          memoryElement,
          parseFloat(server.memoryPercentage || '0'),
          '#10B981',
          '内存',
        );
      }

      // GPU 图表
      const gpuElement = chartRefs.value.get(`${index}-gpu`);
      if (gpuElement) {
        createGaugeChart(gpuElement, parseFloat(server.gpuPercentage || '0'), '#F59E0B', 'GPU');
      }
    });
  }

  // 查看操作
  function handleView(server: ExtendedServerData) {
    message.info(`查看服务器: ${server.modelName} - ${server.serverName}`);
  }

  // 刷新操作
  function handleRefresh(server: ExtendedServerData) {
    message.info(`刷新服务器: ${server.modelName} - ${server.serverName}`);
    fetchServerData();
  }

  // 停止操作
  function handleStop(server: ExtendedServerData) {
    message.warning(`停止服务器: ${server.modelName} - ${server.serverName}`);
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    () => {
      fetchServerData();
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchServerData();
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => fetchServerData(),
  });
</script>

<style scoped>
  .server-card {
    transition: all 0.3s ease;
  }

  .server-card:hover {
    transform: translateY(-1px);
  }
</style>
