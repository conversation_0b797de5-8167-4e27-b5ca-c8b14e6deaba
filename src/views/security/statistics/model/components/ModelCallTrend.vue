<template>
  <div class="model-call-trend bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 标题和筛选控件 -->
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold text-gray-800">模型调用趋势</h3>
      <div class="flex items-center space-x-2">
        <a-button
          v-for="option in timeOptions"
          :key="option.value"
          :type="selectedTimeRange === option.value ? 'primary' : 'default'"
          size="small"
          @click="handleTimeRangeChange(option.value)"
        >
          {{ option.label }}
        </a-button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { Button as AButton } from 'ant-design-vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { selectModelServerTrend } from '@/api/security/modelStats';
  import type { BiModelServerApiCallVo } from '@/api/security/modelStats/model';

  defineOptions({ name: 'ModelCallTrend' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();
  const loading = ref(false);
  const chartData = ref<BiModelServerApiCallVo[]>([]);
  const selectedTimeRange = ref('2'); // 默认按天

  const timeOptions = [
    { label: '按小时', value: '1' },
    { label: '按天', value: '2' },
    { label: '按周', value: '3' },
  ];

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 获取图表数据
  async function fetchChartData() {
    try {
      loading.value = true;
      const params = {
        trentDimension: selectedTimeRange.value,
      };
      
      const response = await selectModelServerTrend(params);
      chartData.value = response || [];
      updateChart();
    } catch (error) {
      console.error('获取模型调用趋势数据失败:', error);
      chartData.value = [];
      updateChart();
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    if (!chartData.value.length) {
      setOptions({
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'middle',
          textStyle: {
            color: '#999',
            fontSize: 14,
          },
        },
      });
      return;
    }

    // 处理数据
    const timeMap = new Map<string, { success: number; failure: number }>();
    const modelMap = new Map<string, Map<string, { success: number; failure: number }>>();

    chartData.value.forEach(item => {
      const time = item.collectTime || '';
      const modelName = item.modelName || '未知模型';
      
      // 总体统计
      if (!timeMap.has(time)) {
        timeMap.set(time, { success: 0, failure: 0 });
      }
      const timeData = timeMap.get(time)!;
      timeData.success += item.callSuccess || 0;
      timeData.failure += item.callFailure || 0;

      // 按模型统计
      if (!modelMap.has(modelName)) {
        modelMap.set(modelName, new Map());
      }
      const modelTimeMap = modelMap.get(modelName)!;
      if (!modelTimeMap.has(time)) {
        modelTimeMap.set(time, { success: 0, failure: 0 });
      }
      const modelTimeData = modelTimeMap.get(time)!;
      modelTimeData.success += item.callSuccess || 0;
      modelTimeData.failure += item.callFailure || 0;
    });

    // 准备图表数据
    const times = Array.from(timeMap.keys()).sort();
    const successData = times.map(time => timeMap.get(time)?.success || 0);
    const failureData = times.map(time => timeMap.get(time)?.failure || 0);

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        borderRadius: 8,
        textStyle: {
          color: '#374151',
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: ['成功调用', '失败调用'],
        top: 10,
        right: 20,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: times,
        axisLabel: {
          rotate: 45,
          formatter: function (value: string) {
            // 根据时间维度格式化显示
            if (selectedTimeRange.value === '1') {
              // 按小时：显示 MM-dd HH
              return value.substring(5);
            } else if (selectedTimeRange.value === '2') {
              // 按天：显示 MM-dd
              return value.substring(5, 10);
            } else {
              // 按周：显示完整日期
              return value;
            }
          },
        },
        axisLine: {
          lineStyle: {
            color: '#e5e7eb',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#e5e7eb',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '调用次数',
        nameTextStyle: {
          color: '#6b7280',
        },
        axisLabel: {
          formatter: '{value}',
          color: '#6b7280',
        },
        axisLine: {
          lineStyle: {
            color: '#e5e7eb',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
          },
        },
      },
      series: [
        {
          name: '成功调用',
          type: 'line',
          stack: 'total',
          data: successData,
          smooth: true,
          lineStyle: {
            color: '#3b82f6',
            width: 2,
          },
          itemStyle: {
            color: '#3b82f6',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                { offset: 1, color: 'rgba(59, 130, 246, 0.05)' },
              ],
            },
          },
        },
        {
          name: '失败调用',
          type: 'line',
          stack: 'total',
          data: failureData,
          smooth: true,
          lineStyle: {
            color: '#f59e0b',
            width: 2,
          },
          itemStyle: {
            color: '#f59e0b',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
                { offset: 1, color: 'rgba(245, 158, 11, 0.05)' },
              ],
            },
          },
        },
      ],
    };

    setOptions(option as any);
  }

  // 时间维度变化处理
  function handleTimeRangeChange(value: string) {
    selectedTimeRange.value = value;
    fetchChartData();
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    () => {
      fetchChartData();
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchChartData();
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => fetchChartData(),
  });
</script>

<style scoped>
  /* 图表容器样式 */
</style>
