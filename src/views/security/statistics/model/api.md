---
title: glasses-admin
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# glasses-admin

Base URLs:

# Authentication

# 003 BI模型监控接口

## GET 01 服务健康监控统计

GET /bi/model/selectModelStats

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": {
    "apiCallSuccessRate": "",
    "averageResponseTime": "",
    "todayCallCount": "",
    "totalCallCount": ""
  },
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RBiCountModelVo](#schemarbicountmodelvo)|

## GET 02 模型服务监控

GET /bi/model/selectModelServerStats

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|modelName|query|string| 否 |模型名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "modelName": "",
      "modelNameEn": "",
      "modelVersion": "",
      "modelRunIp": "",
      "collectTime": "",
      "cpuPercentage": "",
      "memoryPercentage": "",
      "gpuPercentage": "",
      "remark": "",
      "createTime": "",
      "updateTime": "",
      "version": 0
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiModelServerHealthVo](#schemarlistbimodelserverhealthvo)|

## GET 03 模型调用趋势

GET /bi/model/selectModelServerTrend

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|trentDimension|query|string| 否 |1按小时 2按天 3按周|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "token": "",
  "uri": "",
  "data": [
    {
      "modelName": "",
      "modelNameEn": "",
      "modelVersion": "",
      "eventTypeCode": "",
      "eventTypeDesc": "",
      "collectTime": "",
      "callSuccess": 0,
      "remark": "",
      "callFailure": 0,
      "createTime": "",
      "updateTime": "",
      "version": 0
    }
  ],
  "request_time": "",
  "response_time": "",
  "cost_time": "",
  "debug_image_url": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RListBiModelServerApiCallVo](#schemarlistbimodelserverapicallvo)|

# 数据模型

<h2 id="tocS_BiModelServerHealthVo">BiModelServerHealthVo</h2>

<a id="schemabimodelserverhealthvo"></a>
<a id="schema_BiModelServerHealthVo"></a>
<a id="tocSbimodelserverhealthvo"></a>
<a id="tocsbimodelserverhealthvo"></a>

```json
{
  "modelName": "string",
  "modelNameEn": "string",
  "modelVersion": "string",
  "modelRunIp": "string",
  "collectTime": "string",
  "cpuPercentage": "string",
  "memoryPercentage": "string",
  "gpuPercentage": "string",
  "remark": "string",
  "createTime": "string",
  "updateTime": "string",
  "version": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|modelName|string|false|none||模型名称|
|modelNameEn|string|false|none||模型英文名称|
|modelVersion|string|false|none||模型版本|
|modelRunIp|string|false|none||模型运行IP|
|collectTime|string|false|none||采集时间：yyyy-MM-dd HH:mm:ss|
|cpuPercentage|string|false|none||CPU百分比|
|memoryPercentage|string|false|none||内存百分比|
|gpuPercentage|string|false|none||GPU百分比|
|remark|string|false|none||备注|
|createTime|string|false|none||创建时间|
|updateTime|string|false|none||更新时间|
|version|integer|false|none||版本号|

<h2 id="tocS_RListBiModelServerHealthVo">RListBiModelServerHealthVo</h2>

<a id="schemarlistbimodelserverhealthvo"></a>
<a id="schema_RListBiModelServerHealthVo"></a>
<a id="tocSrlistbimodelserverhealthvo"></a>
<a id="tocsrlistbimodelserverhealthvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "modelName": "string",
      "modelNameEn": "string",
      "modelVersion": "string",
      "modelRunIp": "string",
      "collectTime": "string",
      "cpuPercentage": "string",
      "memoryPercentage": "string",
      "gpuPercentage": "string",
      "remark": "string",
      "createTime": "string",
      "updateTime": "string",
      "version": 0
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiModelServerHealthVo](#schemabimodelserverhealthvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiModelServerApiCallVo">BiModelServerApiCallVo</h2>

<a id="schemabimodelserverapicallvo"></a>
<a id="schema_BiModelServerApiCallVo"></a>
<a id="tocSbimodelserverapicallvo"></a>
<a id="tocsbimodelserverapicallvo"></a>

```json
{
  "modelName": "string",
  "modelNameEn": "string",
  "modelVersion": "string",
  "eventTypeCode": "string",
  "eventTypeDesc": "string",
  "collectTime": "string",
  "callSuccess": 0,
  "remark": "string",
  "callFailure": 0,
  "createTime": "string",
  "updateTime": "string",
  "version": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|modelName|string|false|none||模型名称|
|modelNameEn|string|false|none||模型英文名称|
|modelVersion|string|false|none||模型版本|
|eventTypeCode|string|false|none||业务类型代码 ProtocolUtils.EventType|
|eventTypeDesc|string|false|none||业务类型描述|
|collectTime|string|false|none||采集时间：yyyy-MM-dd HH|
|callSuccess|integer(int64)|false|none||调用成功|
|remark|string|false|none||备注|
|callFailure|integer(int64)|false|none||调用失败|
|createTime|string|false|none||创建时间|
|updateTime|string|false|none||更新时间|
|version|integer|false|none||版本号|

<h2 id="tocS_RListBiModelServerApiCallVo">RListBiModelServerApiCallVo</h2>

<a id="schemarlistbimodelserverapicallvo"></a>
<a id="schema_RListBiModelServerApiCallVo"></a>
<a id="tocSrlistbimodelserverapicallvo"></a>
<a id="tocsrlistbimodelserverapicallvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": [
    {
      "modelName": "string",
      "modelNameEn": "string",
      "modelVersion": "string",
      "eventTypeCode": "string",
      "eventTypeDesc": "string",
      "collectTime": "string",
      "callSuccess": 0,
      "remark": "string",
      "callFailure": 0,
      "createTime": "string",
      "updateTime": "string",
      "version": 0
    }
  ],
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[[BiModelServerApiCallVo](#schemabimodelserverapicallvo)]|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

<h2 id="tocS_BiCountModelVo">BiCountModelVo</h2>

<a id="schemabicountmodelvo"></a>
<a id="schema_BiCountModelVo"></a>
<a id="tocSbicountmodelvo"></a>
<a id="tocsbicountmodelvo"></a>

```json
{
  "apiCallSuccessRate": "string",
  "averageResponseTime": "string",
  "todayCallCount": "string",
  "totalCallCount": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|apiCallSuccessRate|string|false|none||接口调用成功率|
|averageResponseTime|string|false|none||平均响应时间|
|todayCallCount|string|false|none||今日调用次数|
|totalCallCount|string|false|none||总调用次数|

<h2 id="tocS_RBiCountModelVo">RBiCountModelVo</h2>

<a id="schemarbicountmodelvo"></a>
<a id="schema_RBiCountModelVo"></a>
<a id="tocSrbicountmodelvo"></a>
<a id="tocsrbicountmodelvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "token": "string",
  "uri": "string",
  "data": {
    "apiCallSuccessRate": "string",
    "averageResponseTime": "string",
    "todayCallCount": "string",
    "totalCallCount": "string"
  },
  "request_time": "string",
  "response_time": "string",
  "cost_time": "string",
  "debug_image_url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|token|string|false|none||none|
|uri|string|false|none||none|
|data|[BiCountModelVo](#schemabicountmodelvo)|false|none||none|
|request_time|string|false|none||请求时间|
|response_time|string|false|none||响应时间|
|cost_time|string|false|none||耗时（单位：秒）|
|debug_image_url|string|false|none||调试图片URL|

