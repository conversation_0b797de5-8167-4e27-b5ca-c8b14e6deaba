<template>
  <div class="center-panel">
    <!-- 中国3D地图 -->
    <div class="map-container">
      <div ref="mapChartRef" class="map-chart"></div>
    </div>

    <!-- 关键指标概览 -->
    <div class="metrics-container">
      <div class="metrics-grid">
        <div v-for="(metric, index) in metricsData" :key="index" class="metric-card">
          <div class="metric-icon-container">
            <img :src="metric.image" :alt="metric.title" class="metric-image" />
          </div>
          <div class="metric-content">
            <div class="metric-value flex items-center">
              <div class="">{{ metric.value }}</div>
              <div class="ml-2">
                <div
                  class="metric-change py-1 px-2 rounded-full"
                  :style="{ backgroundColor: metric.changeColor }"
                >
                  <Icon :icon="metric.changeIcon" :size="12" />
                  <span>{{ metric.change }}</span>
                </div>
              </div>
            </div>
            <div class="metric-title text-gray-400 mt-2">{{ metric.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import echarts from '@/utils/lib/echarts';
  import { getDailyStats } from '@/api/bi/dashboard';
  import type { BiCountDailyVo } from '@/api/bi/model';
  import { message } from 'ant-design-vue';
  import stats1 from '@/assets/images/dashboard/stats-1.png?url';
  import stats2 from '@/assets/images/dashboard/stats-2.png?url';
  import stats3 from '@/assets/images/dashboard/stats-3.png?url';
  import stats4 from '@/assets/images/dashboard/stats-4.png?url';

  // 定义组件属性
  interface Props {
    enableZoom?: boolean;
    enableRotate?: boolean;
    siteName?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    enableZoom: true,
    enableRotate: true,
    siteName: undefined,
  });

  const mapChartRef = ref();
  const { setOptions: setMapOptions, getInstance } = useECharts(mapChartRef, 'dark');

  // 数据状态
  const dailyStatsData = ref<BiCountDailyVo | null>(null);
  const metricsLoading = ref(false);

  const metricsData = ref([
    {
      title: '今日活跃次数',
      value: '0',
      change: '0%',
      changeColor: '#FF6B6B',
      changeIcon: 'ant-design:arrow-up-outlined',
      image: stats1,
    },
    {
      title: '今日作业任务',
      value: '0',
      change: '0%',
      changeColor: '#4ECDC4',
      changeIcon: 'ant-design:arrow-up-outlined',
      image: stats2,
    },
    {
      title: 'AR眼镜使用次数',
      value: '0',
      change: '0%',
      changeColor: '#45B7D1',
      changeIcon: 'ant-design:arrow-up-outlined',
      image: stats3,
    },
    {
      title: '工程车辆检修',
      value: '0',
      change: '0%',
      changeColor: '#FFA726',
      changeIcon: 'ant-design:arrow-up-outlined',
      image: stats4,
    },
  ]);

  const cityData = [
    {
      name: '合肥',
      coord: [117.283, 31.861],
      value: 100,
      status: 'online',
      info: stats4,
    },
  ];

  // 获取今日指标统计数据
  async function fetchDailyStats() {
    try {
      metricsLoading.value = true;
      const data = await getDailyStats({
        siteName: props.siteName,
      });
      dailyStatsData.value = data;
      updateMetricsData(data);
    } catch (error) {
      console.error('获取今日指标统计数据失败:', error);
      message.error('获取今日指标统计数据失败');
    } finally {
      metricsLoading.value = false;
    }
  }

  // 更新指标数据
  function updateMetricsData(data: BiCountDailyVo) {
    metricsData.value = [
      {
        title: '今日活跃次数',
        value: String(data.activeCountDaily || 0),
        change: data.activeCountDailyPercent || '0%',
        changeColor: '#FF6B6B',
        changeIcon: 'ant-design:arrow-up-outlined',
        image: stats1,
      },
      {
        title: '今日作业任务',
        value: String(data.operationCountDaily || 0),
        change: data.operationCountDailyPercent || '0%',
        changeColor: '#4ECDC4',
        changeIcon: 'ant-design:arrow-up-outlined',
        image: stats2,
      },
      {
        title: 'AR眼镜使用次数',
        value: String(data.glassesCountDaily || 0),
        change: data.glassesCountDailyPercent || '0%',
        changeColor: '#45B7D1',
        changeIcon: 'ant-design:arrow-up-outlined',
        image: stats3,
      },
      {
        title: '工程车辆检修',
        value: String(data.vehicleCountDaily || 0),
        change: data.vehicleCountDailyPercent || '0%',
        changeColor: '#FFA726',
        changeIcon: 'ant-design:arrow-up-outlined',
        image: stats4,
      },
    ];
  }

  function initMap() {
    const map3DOption = {
      backgroundColor: 'transparent',
      // tooltip: {
      //   show: true,
      //   trigger: 'item',
      //   backgroundColor: 'rgba(0, 20, 40, 0.95)',
      //   borderColor: '#40e0ff',
      //   borderWidth: 2,
      //   borderRadius: 8,
      //   padding: [12, 16],
      //   textStyle: {
      //     color: '#ffffff',
      //     fontSize: 14,
      //     lineHeight: 1.6,
      //   },
      //   transitionDuration: 0.3,
      //   hideDelay: 200,
      //   triggerOn: 'mousemove|click',
      //   enterable: true,
      //   formatter: function (params) {
      //     if (params.seriesType === 'scatter3D') {
      //       const cityInfo = cityData.find((city) => city.name === params.name);
      //       if (cityInfo) {
      //         return `
      //           <div style="font-weight: bold; color: #40e0ff; margin-bottom: 8px;">
      //             📍 ${cityInfo.name}
      //           </div>
      //           <div style="color: rgba(255, 255, 255, 0.95); font-size: 13px;">
      //             ${cityInfo.info}
      //           </div>
      //         `;
      //       }
      //     }
      //     return '';
      //   },
      // },
      globe: {
        show: false,
      },
      geo3D: {
        map: 'china',
        roam: true,
        itemStyle: {
          color: '#5175e1',
          opacity: 0.85,
          borderWidth: 2,
          borderColor: '#ffffff',
        },
        emphasis: {
          itemStyle: {
            color: '#3b82f6',
            borderColor: '#80ff80',
            borderWidth: 3,
          },
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold',
            textShadow: '0 0 10px rgba(255, 255, 255, 0.8)',
          },
        },
        light: {
          main: {
            color: '#ffffff',
            intensity: 1.2,
            shadow: true,
            shadowQuality: 'medium',
            alpha: 30,
            beta: 40,
          },
          ambient: {
            color: '#40e0ff',
            intensity: 0.4,
          },
        },
        viewControl: {
          autoRotate: false,
          distance: 50,
          alpha: 60,
          beta: 10,
          center: [5, 20, 0],
          animation: true,
          animationDurationUpdate: 800, // 减少动画时长以减少残影
          damping: 0.9, // 增加阻尼以减少惯性残影
          rotateSensitivity: props.enableRotate ? 0.1 : 0, // 根据props控制旋转
          zoomSensitivity: props.enableZoom ? 0.4 : 0, // 根据props控制缩放
          panSensitivity: 1,
          minDistance: 40,
          maxDistance: 200,
        },
        regionHeight: 8,
        postEffect: {
          enable: false,
          bloom: {
            enable: true,
            intensity: 0.1, // 减少bloom强度以减少残影
          },
          // 添加FXAA抗锯齿以改善渲染质量
          FXAA: {
            enable: true,
          },
        },
      },
      series: [
        {
          type: 'scatter3D',
          coordinateSystem: 'geo3D',
          silent: false,
          data: cityData.map((city) => ({
            name: city.name,
            value: [...city.coord, 20],
            itemStyle: {
              color: '#40e0ff',
              opacity: 0.9,
            },
            status: city.status,
            info: city.info,
          })),
          symbol: 'circle',
          symbolSize: 15,
          itemStyle: {
            color: '#40e0ff',
            borderWidth: 1,
            borderColor: '#ffffff',
            opacity: 0.9,
          },
          label: {
            show: true,
            position: 'top',
            color: '#ffffff',
            fontSize: 13,
            fontWeight: 'bold',
            backgroundColor: 'rgba(0,0,0,1)',
            padding: [6, 10],
            borderRadius: 6,
            textShadow: '0 0 8px rgba(0, 0, 0, 0.8)',
            formatter: function (params) {
              return `{icon|} {name|${params.name}}`;
            },
            rich: {
              icon: {
                backgroundColor: {
                  image:
                    'data:image/svg+xml;base64,' +
                    btoa(`
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0 6.03437C0 2.70168 2.70168 0 6.03438 0H17.9656C21.2983 0 24 2.70168 24 6.03438V17.9656C24 21.2983 21.2983 24 17.9656 24H6.03437C2.70168 24 0 21.2983 0 17.9656V6.03437Z" fill="#23BBBB"/>
                      <path d="M7.64584 6.86955C10.0255 4.44322 13.8843 4.44322 16.264 6.86955C18.6435 9.29577 18.6433 13.2294 16.264 15.6557L11.9554 20.0492L7.64584 15.6557C5.26659 13.2294 5.2664 9.29576 7.64584 6.86955ZM11.9554 8.80022C10.6612 8.80022 9.61191 9.84883 9.61166 11.143C9.61166 12.4374 10.661 13.4867 11.9554 13.4867C13.2496 13.4865 14.2982 12.4372 14.2982 11.143C14.2979 9.84898 13.2494 8.80046 11.9554 8.80022Z" fill="white"/>
                    </svg>
                  `),
                },
                width: 16,
                height: 16,
                align: 'center',
              },
              name: {
                color: '#ffffff',
                fontSize: 13,
                fontWeight: 'bold',
                textShadow: '0 0 8px rgba(0, 0, 0, 0.8)',
                padding: [0, 0, 0, 4],
              },
            },
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
              borderWidth: 3,
              borderColor: '#00ff88',
              color: '#00ff88',
              shadowColor: '#40e0ff',
              shadowBlur: 15,
            },
            symbolSize: 18,
            label: {
              show: true,
              fontSize: 14,
              color: '#ffffff',
              backgroundColor: 'rgba(0, 20, 40, 0.95)',
              padding: [8, 12],
              borderRadius: 8,
              borderColor: '#00ff88',
              borderWidth: 1,
              formatter: function (params) {
                return `🌟 ${params.name}`;
              },
            },
          },
          blendMode: 'source-over',
          zlevel: 10,
        },
      ],
      animation: true,
      animationDuration: 1000, // 减少动画时长以减少残影
      animationEasing: 'cubicOut',
    };

    try {
      setMapOptions(map3DOption);
    } catch (error) {
      console.error('3D地图初始化失败:', error);
    }
  }

  async function loadChinaMapData() {
    try {
      const response = await fetch('/json/100000_full.json');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoJson = await response.json();

      if (!geoJson || !geoJson.features) {
        throw new Error('Invalid map data format');
      }

      echarts.registerMap('china', geoJson);

      requestAnimationFrame(() => {
        initMap();
      });
    } catch (error) {
      console.error('地图数据加载失败:', error);
    }
  }

  // 监听站点变化，重新获取数据
  watch(
    () => props.siteName,
    async () => {
      await fetchDailyStats();
    },
  );

  onMounted(async () => {
    // 获取今日指标统计数据
    await fetchDailyStats();

    // 加载地图数据
    setTimeout(() => {
      loadChinaMapData();
    }, 100);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1400px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 15px;
    }

    .metrics-container {
      height: 240px;
    }

    .metric-value {
      font-size: 24px;
    }
  }

  @media (max-width: 1200px) {
    .map-chart {
      min-height: 300px;
    }

    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 12px;
    }

    .metrics-container {
      height: 120px;
    }

    .metric-title {
      font-size: 12px;
    }

    .metric-value {
      font-size: 22px;
    }

    .metric-change {
      font-size: 11px;
    }
  }

  .center-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .map-container {
    /* 确保容器有明确的层叠上下文 */
    position: relative;
    z-index: 1;
    flex: 1;
    overflow: hidden;

    /* 启用硬件加速以改善渲染性能 */
    transform: translateZ(0);
    background-color: transparent;
  }

  .map-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .map-title {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .map-chart {
    height: 100%;
    min-height: 300px;
    overflow: hidden;

    /* 启用硬件加速 */
    transform: translateZ(0);
    backface-visibility: hidden;

    /* 确保清晰的边界 */
    border-radius: 0;

    /* 防止残影的关键样式 */
    background-color: transparent;

    /* 优化渲染性能 */
    will-change: transform;
  }

  .metrics-container {
    flex-shrink: 0;
    height: 120px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    height: 100%;
  }

  .metric-card {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .metric-card:hover {
    transform: translateY(-2px);
  }

  .metric-icon-container {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    margin-right: 16px;
    overflow: hidden;
  }

  .metric-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .metric-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
  }

  .metric-title {
    margin-bottom: 4px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
  }

  .metric-value {
    margin-bottom: 2px;
    color: #fff;
    font-size: 28px;
    font-weight: 700;
    line-height: 1;
  }

  .metric-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
  }
</style>
