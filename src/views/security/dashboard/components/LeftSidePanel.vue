<template>
  <div class="left-side-panel">
    <!-- 业务异常情况统计 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">业务异常情况统计</h3>
        <div class="card-controls">
          <Select
            v-model:value="businessExceptionPeriod"
            class="dark-select"
            style="width: 120px"
            size="small"
          >
            <SelectOption value="7">近7天</SelectOption>
            <SelectOption value="15">近15天</SelectOption>
            <SelectOption value="30">近30天</SelectOption>
          </Select>
          <!-- <Button type="primary" size="small" @click="exportBusinessData">
            <Icon icon="ant-design:export-outlined" />
            导出
          </Button> -->
        </div>
      </div>

      <div class="card-content">
        <div ref="businessExceptionChartRef" class="chart-container"></div>
      </div>
    </div>

    <!-- 工程车辆检修情况统计 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">工程车辆检修情况统计</h3>
        <div class="card-controls">
          <Select
            v-model:value="vehicleInspectionPeriod"
            class="dark-select"
            style="width: 120px"
            size="small"
          >
            <SelectOption value="1">近1个月</SelectOption>
            <SelectOption value="3">近3个月</SelectOption>
            <SelectOption value="6">近6个月</SelectOption>
          </Select>
          <!-- <Button type="primary" size="small" @click="exportVehicleData">
            <Icon icon="ant-design:export-outlined" />
            导出
          </Button> -->
        </div>
      </div>

      <div class="card-content">
        <div ref="vehicleInspectionChartRef" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import { Select, SelectOption, message } from 'ant-design-vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import {
    getEventExceptionStats,
    getVehicleDailyEventStats,
    calculateDateRange,
    calculateDateRangeByMonths,
  } from '@/api/bi/dashboard';
  import type { BiEventExceptionStatsVo, BiVehicleDailyEventStatsVo } from '@/api/bi/model';

  // 定义 props
  interface Props {
    siteName?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    siteName: undefined,
  });

  const businessExceptionPeriod = ref('7');
  const vehicleInspectionPeriod = ref('1');

  const businessExceptionChartRef = ref();
  const vehicleInspectionChartRef = ref();

  const { setOptions: setBusinessOptions } = useECharts(businessExceptionChartRef, 'dark');
  const { setOptions: setVehicleOptions } = useECharts(vehicleInspectionChartRef, 'dark');

  // 数据状态
  const businessExceptionApiData = ref<BiEventExceptionStatsVo[]>([]);
  const vehicleInspectionApiData = ref<BiVehicleDailyEventStatsVo[]>([]);
  const businessLoading = ref(false);
  const vehicleLoading = ref(false);

  // 获取业务异常情况统计数据
  async function fetchBusinessExceptionData() {
    try {
      businessLoading.value = true;
      const days = parseInt(businessExceptionPeriod.value);
      const { startTime, endTime } = calculateDateRange(days);

      const data = await getEventExceptionStats({
        startTime,
        endTime,
        siteName: props.siteName,
      });

      businessExceptionApiData.value = data || [];
    } catch (error) {
      console.error('获取业务异常情况统计数据失败:', error);
      message.error('获取业务异常情况统计数据失败');
      businessExceptionApiData.value = [];
    } finally {
      businessLoading.value = false;
    }
  }

  // 获取工程车辆检修情况统计数据
  async function fetchVehicleInspectionData() {
    try {
      vehicleLoading.value = true;
      const months = parseInt(vehicleInspectionPeriod.value);
      const { startTime, endTime } = calculateDateRangeByMonths(months);

      const data = await getVehicleDailyEventStats({
        startTime,
        endTime,
        siteName: props.siteName,
      });

      vehicleInspectionApiData.value = data || [];
    } catch (error) {
      console.error('获取工程车辆检修情况统计数据失败:', error);
      message.error('获取工程车辆检修情况统计数据失败');
      vehicleInspectionApiData.value = [];
    } finally {
      vehicleLoading.value = false;
    }
  }

  // 初始化业务异常情况图表
  function initBusinessExceptionChart() {
    if (!businessExceptionApiData.value.length) {
      return;
    }

    const apiData = businessExceptionApiData.value;
    const dates = apiData.map((item) => item.daily);
    const smartWork = apiData.map((item) => item.totalOperationException);
    const smartMaintenance = apiData.map((item) => item.totalVehicleException);
    const smartWarehouse = apiData.map((item) => item.totalMaterialxception);

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
      },
      legend: {
        data: ['智慧作业', '智慧检修', '智慧料库'],
        top: '0%',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      },
      series: [
        {
          name: '智慧作业',
          type: 'bar',
          stack: 'total',
          data: smartWork,
          itemStyle: {
            color: '#26B99A',
            borderRadius: 5,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.1)',
          },
        },
        {
          name: '智慧检修',
          type: 'bar',
          stack: 'total',
          data: smartMaintenance,
          itemStyle: {
            color: '#3498DB',
            borderRadius: 5,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.1)',
          },
        },
        {
          name: '智慧料库',
          type: 'bar',
          stack: 'total',
          data: smartWarehouse,
          itemStyle: {
            color: '#E67E22',
            borderRadius: 5,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.1)',
          },
        },
      ],
    };
    setBusinessOptions(option);
  }

  // 初始化车辆检修情况图表
  function initVehicleInspectionChart() {
    if (!vehicleInspectionApiData.value.length) {
      return;
    }

    const apiData = vehicleInspectionApiData.value;
    const dates = apiData.map((item) => item.daily);
    const inspectionCount = apiData.map((item) => item.total);
    const exceptionCount = apiData.map((item) => item.totalExcep);

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
      },
      legend: {
        data: ['检修次数', '异常数量'],
        top: '0%',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '25%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
      },
      yAxis: [
        {
          type: 'value',
          name: '检修次数',
          position: 'left',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
        {
          type: 'value',
          name: '异常数量',
          position: 'right',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
      ],
      series: [
        {
          name: '检修次数',
          type: 'line',
          yAxisIndex: 0,
          data: inspectionCount,
          smooth: true,
          lineStyle: {
            color: '#1F4E79',
            width: 3,
            shadowColor: 'rgba(31, 78, 121, 0.5)',
            shadowBlur: 10,
          },
          itemStyle: { color: '#1F4E79' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(31, 78, 121, 0.6)' },
                { offset: 1, color: 'rgba(31, 78, 121, 0.1)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(31, 78, 121, 0.8)',
            },
          },
        },
        {
          name: '异常数量',
          type: 'line',
          yAxisIndex: 1,
          data: exceptionCount,
          smooth: true,
          lineStyle: {
            color: '#E67E22',
            width: 3,
            shadowColor: 'rgba(230, 126, 34, 0.5)',
            shadowBlur: 10,
          },
          itemStyle: { color: '#E67E22' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(230, 126, 34, 0.6)' },
                { offset: 1, color: 'rgba(230, 126, 34, 0.1)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(230, 126, 34, 0.8)',
            },
          },
        },
      ],
    };
    setVehicleOptions(option);
  }

  // 导出数据
  function exportBusinessData() {
    message.success('业务异常数据导出成功');
  }

  function exportVehicleData() {
    message.success('车辆检修数据导出成功');
  }

  // 监听时间段变化，重新获取数据
  watch(businessExceptionPeriod, async () => {
    await fetchBusinessExceptionData();
    initBusinessExceptionChart();
  });

  watch(vehicleInspectionPeriod, async () => {
    await fetchVehicleInspectionData();
    initVehicleInspectionChart();
  });

  // 监听站点变化，重新获取数据
  watch(() => props.siteName, async () => {
    await Promise.all([fetchBusinessExceptionData(), fetchVehicleInspectionData()]);
    setTimeout(() => {
      initBusinessExceptionChart();
      initVehicleInspectionChart();
    }, 100);
  });

  // 监听数据变化，重新初始化图表
  watch(businessExceptionApiData, initBusinessExceptionChart, { deep: true });
  watch(vehicleInspectionApiData, initVehicleInspectionChart, { deep: true });

  onMounted(async () => {
    // 初始化时获取数据
    await Promise.all([fetchBusinessExceptionData(), fetchVehicleInspectionData()]);

    // 延迟初始化图表，确保DOM已渲染
    setTimeout(() => {
      initBusinessExceptionChart();
      initVehicleInspectionChart();
    }, 100);
  });
</script>

<style scoped>
  @import '../styles/common.scss';

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .card-header {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }

    .card-controls {
      align-self: auto;
    }

    .card-title {
      font-size: 15px;
    }
  }

  .left-side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .panel-card {
    flex: 1;
    overflow: hidden;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
  }

  .card-title {
    margin: 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .card-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
  }

  .chart-container {
    flex: 1;
    min-height: 200px;
  }
</style>
