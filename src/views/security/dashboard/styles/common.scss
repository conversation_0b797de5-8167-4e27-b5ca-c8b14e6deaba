/* Dashboard 公共样式 - 深色模式 Select 组件样式优化 */

/* 深色模式 Select 组件样式优化 */
:deep(.dark-select) {
  .ant-select-selector {
    transition: all 0.3s ease;
    border-color: rgba(255, 255, 255, 15%) !important;
    background-color: rgba(255, 255, 255, 8%) !important;
    color: rgba(255, 255, 255, 85%) !important;
    backdrop-filter: blur(8px);
  }

  .ant-select-selector:hover {
    border-color: rgba(64, 169, 255, 60%) !important;
    background-color: rgba(255, 255, 255, 12%) !important;
    box-shadow: 0 0 8px rgba(64, 169, 255, 20%);
  }

  .ant-select-focused .ant-select-selector {
    border-color: #40a9ff !important;
    background-color: rgba(255, 255, 255, 12%) !important;
    box-shadow: 0 0 0 2px rgba(64, 169, 255, 20%) !important;
  }

  .ant-select-selection-item {
    color: rgba(255, 255, 255, 85%) !important;
    font-weight: 500;
  }

  .ant-select-arrow {
    color: rgba(255, 255, 255, 65%) !important;
  }

  .ant-select-arrow:hover {
    color: rgba(255, 255, 255, 85%) !important;
  }
}

/* 下拉菜单样式 */
:deep(.ant-select-dropdown) {
  border: 1px solid rgba(255, 255, 255, 15%) !important;
  border-radius: 8px;
  background-color: rgba(30, 30, 30, 95%) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 40%) !important;
  backdrop-filter: blur(12px);
}

:deep(.ant-select-item) {
  transition: all 0.2s ease;
  background-color: transparent !important;
  color: rgba(255, 255, 255, 85%) !important;
}

:deep(.ant-select-item:hover) {
  background-color: rgba(64, 169, 255, 15%) !important;
  color: #fff !important;
}

:deep(.ant-select-item-option-selected) {
  background-color: rgba(64, 169, 255, 25%) !important;
  color: #fff !important;
  font-weight: 600;
}

:deep(.ant-select-item-option-selected:hover) {
  background-color: rgba(64, 169, 255, 35%) !important;
}
