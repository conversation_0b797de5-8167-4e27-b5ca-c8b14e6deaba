<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">模型服务状态组件测试</h1>

      <!-- 模型服务状态网格 -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">模型服务状态网格</h2>
        <ModelServerStatus ref="modelServerStatusRef" />
      </div>

      <!-- 刷新按钮 -->
      <div class="text-center">
        <a-button type="primary" @click="refreshData">
          <template #icon>
            <Icon icon="ant-design:reload-outlined" />
          </template>
          刷新数据
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Button as AButton } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import ModelServerStatus from '@/views/security/statistics/model/components/ModelServerStatus.vue';

  const modelServerStatusRef = ref();

  // 刷新数据
  function refreshData() {
    modelServerStatusRef.value?.refresh();
  }
</script>
