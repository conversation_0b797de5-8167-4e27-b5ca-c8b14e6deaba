import * as echarts from 'echarts/core';
// 导入 echarts-gl 以支持 3D 图表
import 'echarts-gl';

// 导入 3D 相关组件
import { Map3<PERSON>hart, Scatter3DChart } from 'echarts-gl/charts';
import { Grid3DComponent, Geo3DComponent } from 'echarts-gl/components';

import {
  <PERSON><PERSON>hart,
  LineChart,
  PieChart,
  MapChart,
  PictorialBarChart,
  RadarChart,
  ScatterChart,
  EffectScatterChart,
  GaugeChart,
} from 'echarts/charts';

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  LegendComponent,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  CalendarComponent,
  GraphicComponent,
  GeoComponent,
} from 'echarts/components';

import { SVGRenderer } from 'echarts/renderers';

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  GeoComponent,
  BarChart,
  LineChart,
  PieChart,
  MapChart,
  RadarChart,
  SVGRenderer,
  PictorialBarChart,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  CalendarComponent,
  GraphicComponent,
  ScatterChart,
  EffectScatterChart,
  GaugeChart,
  // 3D 组件
  Map3DChart,
  Scatter3DChart,
  Grid3DComponent,
  Geo3DComponent,
]);

export default echarts;
